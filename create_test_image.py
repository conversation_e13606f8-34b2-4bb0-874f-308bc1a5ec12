#!/usr/bin/env python3
"""
创建测试图片用于水印去除测试
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    """创建一个带有水印的测试图片"""
    # 创建一个512x512的图片
    img = Image.new('RGB', (512, 512), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 添加一些基本内容
    draw.rectangle([50, 50, 462, 462], fill='white', outline='black', width=2)
    
    # 添加文字（模拟内容）
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    draw.text((100, 200), "测试图片内容", fill='black', font=font)
    draw.text((100, 250), "Test Image Content", fill='black', font=font)
    
    # 添加水印（模拟水印）
    try:
        watermark_font = ImageFont.truetype("arial.ttf", 36)
    except:
        watermark_font = ImageFont.load_default()
    
    # 半透明水印
    watermark = Image.new('RGBA', (512, 512), (255, 255, 255, 0))
    watermark_draw = ImageDraw.Draw(watermark)
    
    # 添加对角线水印文字
    watermark_draw.text((150, 300), "WATERMARK", fill=(255, 0, 0, 128), font=watermark_font)
    watermark_draw.text((200, 350), "水印测试", fill=(255, 0, 0, 128), font=watermark_font)
    
    # 合并图片和水印
    img = img.convert('RGBA')
    img = Image.alpha_composite(img, watermark)
    img = img.convert('RGB')
    
    # 保存图片
    filename = "test_image_with_watermark.png"
    img.save(filename, "PNG")
    print(f"✅ 测试图片已创建: {filename}")
    print(f"📊 文件大小: {os.path.getsize(filename)} bytes")
    
    return filename

if __name__ == "__main__":
    try:
        create_test_image()
    except ImportError:
        print("❌ 需要安装PIL库: pip install Pillow")
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
