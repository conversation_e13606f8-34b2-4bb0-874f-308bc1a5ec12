#!/usr/bin/env python3
"""
简化的水印去除功能测试
"""

import requests
import os
import time
import random
import base64
import hmac
import hashlib
from datetime import datetime, timezone

def get_x_ebg_param():
    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
    p = timestamp
    x_ebg_param = base64.b64encode(p.encode("utf-8")).decode("utf-8")
    return timestamp, x_ebg_param

def downloadFile(url, filename=None):
    """下载图片文件"""
    filename = filename or f"test_{int(time.time())}.png"
    try:
        print(f"📥 开始下载图片: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        with open(filename, "wb") as f:
            f.write(response.content)
        
        print(f"✅ 下载完成: {filename}")
        print(f"📊 文件大小: {len(response.content)} bytes")
        return filename
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None

def watermarkremover(filename):
    """去除图片水印"""
    try:
        print(f"🔄 开始水印去除处理: {filename}")
        
        if not os.path.exists(filename):
            print(f"❌ 文件不存在: {filename}")
            return None
        
        pixb_cl_id = str(random.randint(1000000000, 9999999999))
        timestamp, x_ebg_param = get_x_ebg_param()
        
        n = "A4nzUYcDOZ"
        t = f"POST/service/public/transformation/v1.0/predictions/wm/remove{timestamp}{pixb_cl_id}"
        x_ebg_signature = hmac.new(
            n.encode("utf-8"), t.encode("utf-8"), hashlib.sha256
        ).hexdigest()
        
        url = "https://api.watermarkremover.io/service/public/transformation/v1.0/predictions/wm/remove"
        payload = {"input.rem_text": "false", "input.rem_logo": "false", "retention": "1d"}
        
        print(f"📤 上传图片到水印去除服务...")
        
        with open(filename, "rb") as file_handle:
            files = [("input.image", (filename, file_handle.read(), "image/png"))]
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "x-ebg-signature": x_ebg_signature,
                "pixb-cl-id": pixb_cl_id,
                "x-ebg-param": x_ebg_param,
                "origin": "https://www.watermarkremover.io",
                "referer": "https://www.watermarkremover.io/",
            }
            
            response = requests.post(url, data=payload, files=files, headers=headers, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ 上传失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
        
        response_data = response.json()
        result_id = response_data.get("_id")
        
        if not result_id:
            print(f"❌ 未获取到任务ID: {response_data}")
            return None
        
        print(f"✅ 上传成功，任务ID: {result_id}")
        print(f"⏳ 等待处理完成...")
        
        # 检查处理状态
        status_url = f"https://api.watermarkremover.io/service/public/transformation/v1.0/predictions/{result_id}"
        status_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "origin": "https://www.watermarkremover.io",
            "referer": "https://www.watermarkremover.io/",
        }
        
        # 最多等待60秒
        for attempt in range(60):
            try:
                status_response = requests.get(status_url, headers=status_headers, timeout=10)
                
                if status_response.status_code != 200:
                    print(f"⚠️ 状态查询失败，状态码: {status_response.status_code}")
                    time.sleep(1)
                    continue
                
                status_data = status_response.json()
                status = status_data.get("status", "UNKNOWN")
                
                print(f"📊 处理状态: {status} (尝试 {attempt + 1}/60)")
                
                if status == "SUCCESS":
                    output_urls = status_data.get("output", [])
                    if output_urls and len(output_urls) > 0:
                        result_url = output_urls[0]
                        print(f"🎉 水印去除成功: {result_url}")
                        
                        # 清理临时文件
                        try:
                            if os.path.exists(filename):
                                os.remove(filename)
                                print(f"🗑️ 临时文件已删除: {filename}")
                        except OSError as e:
                            print(f"⚠️ 删除临时文件失败: {e}")
                        
                        return result_url
                    else:
                        print(f"❌ 处理成功但未获取到结果URL: {status_data}")
                        return None
                
                elif status == "FAILED":
                    print(f"❌ 水印去除失败: {status_data}")
                    return None
                
                elif status in ["PENDING", "PROCESSING"]:
                    # 继续等待
                    time.sleep(1)
                    continue
                else:
                    print(f"⚠️ 未知状态: {status}")
                    time.sleep(1)
                    continue
                    
            except Exception as e:
                print(f"⚠️ 状态查询出错 (尝试 {attempt + 1}): {e}")
                time.sleep(1)
                continue
        
        print(f"⏰ 水印去除超时 (60秒)")
        return None
        
    except Exception as e:
        print(f"❌ 水印去除过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 确保清理临时文件
        try:
            if os.path.exists(filename):
                os.remove(filename)
                print(f"🗑️ 最终清理临时文件: {filename}")
        except:
            pass

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 水印去除功能测试")
    print("=" * 60)

    # 使用本地测试图片
    test_image_file = "test_image_with_watermark.png"

    if not os.path.exists(test_image_file):
        print(f"❌ 测试图片不存在: {test_image_file}")
        print("💡 请先运行 python create_test_image.py 创建测试图片")
        return

    print(f"📁 使用本地测试图片: {test_image_file}")
    print(f"📊 文件大小: {os.path.getsize(test_image_file)} bytes")

    # 复制测试图片用于处理
    import shutil
    filename = f"watermark_test_{int(time.time())}.png"
    shutil.copy2(test_image_file, filename)
    print(f"📋 复制测试图片: {filename}")
    
    # 测试水印去除
    result_url = watermarkremover(filename)
    
    if result_url:
        print(f"\n🎉 水印去除测试成功!")
        print(f"🔗 结果URL: {result_url}")
        
        # 验证结果URL
        test_result_filename = downloadFile(result_url, "watermark_removed_result.png")
        if test_result_filename:
            print(f"✅ 结果验证成功: {test_result_filename}")
            # 清理测试文件
            try:
                os.remove(test_result_filename)
                print(f"🗑️ 清理结果文件: {test_result_filename}")
            except:
                pass
        
        print("\n🎉 水印去除功能正常工作!")
    else:
        print("\n❌ 水印去除测试失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
