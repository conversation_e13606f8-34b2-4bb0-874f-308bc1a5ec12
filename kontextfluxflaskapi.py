import json
import time
import uuid
import base64
import hashlib
import asyncio
import websocket
from io import Bytes<PERSON>
from typing import List, Optional
import os
import hmac
import random
from datetime import datetime, timezone

import requests
import httpx
from flask import Flask, request, Response, jsonify, stream_with_context, render_template
# from pydantic import BaseModel, Field  # 暂时未使用
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


class KontextFluxEncryptor:
    """Replicates the encryption logic from a.js to generate the 'xtx' header hash."""

    def __init__(self, config_data):
        self.kis = config_data["kis"]
        self.ra1 = config_data["ra1"]
        self.ra2 = config_data["ra2"]
        self.random = config_data["random"]

    def _aes_decrypt(self, key, iv, ciphertext_b64):
        """Decrypts AES-CBC base64 encoded data."""
        cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, iv.encode("utf-8"))
        decoded_ciphertext = base64.b64decode(ciphertext_b64)
        decrypted_padded = cipher.decrypt(decoded_ciphertext)
        return unpad(decrypted_padded, AES.block_size).decode("utf-8")

    def _aes_encrypt(self, key, iv, plaintext):
        """Encrypts plaintext with AES-CBC and returns a base64 encoded string."""
        cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, iv.encode("utf-8"))
        padded_data = pad(plaintext.encode("utf-8"), AES.block_size)
        encrypted_data = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted_data).decode("utf-8")

    def get_xtx_hash(self, payload):
        """Generates the final MD5 hash for the 'xtx' header."""
        sorted_keys = sorted(payload.keys())
        serialized_parts = []
        for key in sorted_keys:
            value = payload[key]
            stringified_value = json.dumps(value, separators=(",", ":"), ensure_ascii=False)
            safe_value = stringified_value.replace("<", "").replace(">", "")
            encoded_value = base64.b64encode(safe_value.encode("utf-8")).decode("utf-8")
            serialized_parts.append(f"{key}={encoded_value}")

        serialized_payload = "".join(serialized_parts)
        decoded_kis = base64.b64decode(self.kis).split(b"=sj+Ow2R/v")
        random_str = str(self.random)

        y = int(random_str[0])
        b = int(random_str[-1])
        k = int(random_str[2 : 2 + y])
        s_idx = int(random_str[4 + y : 4 + y + b])

        intermediate_key = decoded_kis[k].decode("utf-8")
        intermediate_iv = decoded_kis[s_idx].decode("utf-8")

        main_key = self._aes_decrypt(intermediate_key, intermediate_iv, self.ra1)
        main_iv = self._aes_decrypt(intermediate_key, intermediate_iv, self.ra2)

        encrypted_payload = self._aes_encrypt(main_key, main_iv, serialized_payload)
        final_hash = hashlib.md5(encrypted_payload.encode("utf-8")).hexdigest()
        return final_hash


def get_config():
    """Get kontextflux configuration."""
    url = "https://api.kontextflux.com/client/common/getConfig"
    payload = {"token": None, "referrer": ""}
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "Origin": "https://kontextflux.com",
        "Referer": "https://kontextflux.com/",
    }
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    response.raise_for_status()
    return response.json()["data"]


async def upload_file(config, image_bytes: bytes, filename: str = "image.png"):
    """Upload image file to kontextflux."""
    url = "https://api.kontextflux.com/client/resource/uploadFile"
    files = [("file", (filename, BytesIO(image_bytes), "null"))]
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Authorization": config["token"],
        "xtx": KontextFluxEncryptor(config).get_xtx_hash({}),
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(url, files=files, headers=headers)
        response.raise_for_status()
        return response.json()["data"]


def create_draw_task(config, prompt: str, keys: List[str] = [], size: str = "auto"):
    """Create image generation task."""
    url = "https://api.kontextflux.com/client/styleAI/draw"
    payload = {"keys": keys, "prompt": prompt, "size": size}
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "Authorization": config["token"],
        "xtx": KontextFluxEncryptor(config).get_xtx_hash(payload),
    }
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    response.raise_for_status()
    return response.json()["data"]["id"]


async def process_image_url(image_url: str) -> Optional[bytes]:
    """Process image URL (base64 or http/https) and return image bytes."""
    try:
        if image_url.startswith('data:image/'):
            # Handle base64 encoded image
            _, encoded = image_url.split(",", 1)
            return base64.b64decode(encoded)
        elif image_url.startswith(('http://', 'https://')):
            # Handle HTTP(S) URL
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url, timeout=60, follow_redirects=True)
                response.raise_for_status()
                return response.content
        else:
            print(f"Unsupported image URL format: {image_url[:30]}...")
            return None
    except Exception as e:
        print(f"Error processing image: {e}")
        return None


# 直接在代码中定义API密钥
VALID_CLIENT_KEYS = {"sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2"}

# Flask App
app = Flask(__name__)


def authenticate_client():
    """Authenticate client based on API key in Authorization header"""
    if not VALID_CLIENT_KEYS:
        return {"error": "Service unavailable: Client API keys not configured."}, 503
    
    auth_header = request.headers.get("Authorization", "")
    if not auth_header.startswith("Bearer "):
        return {"error": "API key required in Authorization header."}, 401
    
    api_key = auth_header[7:]  # Remove 'Bearer ' prefix
    if api_key not in VALID_CLIENT_KEYS:
        return {"error": "Invalid client API key."}, 403
    
    return None


@app.route("/")
def index():
    """Web界面首页"""
    return render_template('index.html')

@app.route("/test")
def api_test():
    """API测试页面"""
    return render_template('api_test.html')

@app.route("/api")
def api_info():
    """API服务状态页面"""
    return jsonify({
        "service": "KontextFlux OpenAI API Adapter",
        "status": "running",
        "version": "1.0.0",
        "endpoints": [
            "GET /",
            "GET /api",
            "GET /health",
            "GET /v1/models",
            "POST /v1/chat/completions"
        ],
        "api_keys_configured": len(VALID_CLIENT_KEYS),
        "message": "API is working properly"
    })


@app.route("/health")
def health_check():
    """健康检查端点"""
    return jsonify({"status": "healthy", "service": "kontextflux-api", "timestamp": int(time.time())})

@app.route("/api/generate", methods=["POST"])
def generate_image_api():
    """Web界面图像生成API"""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '')
        aspect_ratio = data.get('aspect_ratio', 'auto')
        image_urls = data.get('image_urls', [])  # 新增：接收图片URL列表

        if not prompt and not image_urls:
            return jsonify({"error": "请提供图像描述或上传图片"}), 400

        # 获取配置
        config = get_config()

        # 处理并上传图像
        uploaded_keys = []
        if image_urls:
            for i, image_url in enumerate(image_urls):
                image_bytes = asyncio.run(process_image_url(image_url))
                if image_bytes:
                    upload_result = asyncio.run(upload_file(config, image_bytes, f"web_image_{i}.png"))
                    uploaded_keys.append(upload_result["key"])

        # 创建绘图任务，传入上传的图片keys
        draw_id = create_draw_task(config, prompt, uploaded_keys, aspect_ratio)

        return jsonify({
            "success": True,
            "task_id": draw_id,
            "message": "图像生成任务已创建",
            "uploaded_images": len(uploaded_keys)  # 返回上传的图片数量
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/api/status/<task_id>")
def check_task_status(task_id):
    """检查任务状态"""
    try:
        config = get_config()

        # 实际检查任务状态
        e = {"token": config["token"], "id": task_id}
        xtx = KontextFluxEncryptor(config).get_xtx_hash(e)
        url = f"wss://api.kontextflux.com/client/styleAI/checkWs?xtx={xtx}"

        try:
            ws = websocket.create_connection(url, timeout=10)
            ws.send(json.dumps(e))

            # 设置接收超时
            ws.settimeout(5)
            msg = ws.recv()
            data = json.loads(msg)
            ws.close()

            if data.get("content", {}).get("photo"):
                # 任务完成
                return jsonify({
                    "task_id": task_id,
                    "status": "completed",
                    "progress": 100,
                    "result_url": data["content"]["photo"]["url"]
                })
            else:
                # 任务进行中
                progress = data.get("content", {}).get("progress", 0)
                return jsonify({
                    "task_id": task_id,
                    "status": "processing",
                    "progress": progress,
                    "result_url": None
                })

        except websocket.WebSocketTimeoutException:
            return jsonify({
                "task_id": task_id,
                "status": "processing",
                "progress": 50,
                "result_url": None
            })
        except Exception as ws_error:
            print(f"WebSocket error: {ws_error}")
            return jsonify({
                "task_id": task_id,
                "status": "error",
                "progress": 0,
                "error": str(ws_error)
            }), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/api/download-image", methods=["POST"])
def download_image_proxy():
    """图片代理下载API"""
    try:
        data = request.get_json()
        image_url = data.get('url')

        if not image_url:
            return jsonify({"error": "缺少图片URL"}), 400

        print(f"代理下载图片: {image_url}")

        # 使用requests下载图片，添加适当的请求头
        import requests
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        response = requests.get(image_url, headers=headers, timeout=30, stream=True)
        response.raise_for_status()

        print(f"下载成功，状态码: {response.status_code}, 内容类型: {response.headers.get('content-type')}")

        # 获取文件扩展名
        content_type = response.headers.get('content-type', 'image/jpeg')
        if 'png' in content_type:
            filename = "generated_image.png"
        elif 'webp' in content_type:
            filename = "generated_image.webp"
        else:
            filename = "generated_image.jpg"

        # 返回图片数据
        return Response(
            response.content,
            mimetype=content_type,
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"',
                'Content-Length': str(len(response.content)),
                'Cache-Control': 'no-cache'
            }
        )

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {str(e)}")
        return jsonify({"error": f"网络请求失败: {str(e)}"}), 500
    except Exception as e:
        print(f"下载错误: {str(e)}")
        return jsonify({"error": f"下载失败: {str(e)}"}), 500


@app.route("/v1/models", methods=["GET"])
def list_models():
    """List available models"""
    auth_error = authenticate_client()
    if auth_error:
        return auth_error
    
    return jsonify({
        "object": "list",
        "data": [
            {
                "id": "kontext-flux",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "kontextflux"
            }
        ]
    })


@app.route("/v1/chat/completions", methods=["POST"])
def chat_completions():
    """Create chat completion using KontextFlux backend"""
    auth_error = authenticate_client()
    if auth_error:
        return auth_error
    
    request_data = request.json
    
    if request_data.get("model") != "kontext-flux":
        return {"error": f"Model '{request_data.get('model')}' not found."}, 404
    
    if not request_data.get("messages"):
        return {"error": "No messages provided in the request."}, 400
    
    # Extract prompt and images from messages
    prompt_parts = []
    image_urls = []
    
    for message in request_data.get("messages", []):
        content = message.get("content", "")
        if isinstance(content, str):
            prompt_parts.append(content)
        elif isinstance(content, list):
            for part in content:
                if part.get("type") == "text" and part.get("text"):
                    prompt_parts.append(part["text"])
                elif part.get("type") == "image_url" and part.get("image_url", {}).get("url"):
                    image_urls.append(part["image_url"]["url"])
    
    prompt = " ".join(filter(None, prompt_parts))

    if not prompt and not image_urls:
        return {"error": "Request must contain text prompt or at least one image."}, 400

    # 获取宽高比参数
    aspect_ratio = request_data.get("aspect_ratio", "auto")

    try:
        # Get kontextflux config
        config = get_config()

        # Process and upload images
        uploaded_keys = []
        if image_urls:
            for i, image_url in enumerate(image_urls):
                image_bytes = asyncio.run(process_image_url(image_url))
                if image_bytes:
                    upload_result = asyncio.run(upload_file(config, image_bytes, f"image_{i}.png"))
                    uploaded_keys.append(upload_result["key"])

        # Create draw task with aspect ratio
        draw_id = create_draw_task(config, prompt, uploaded_keys, aspect_ratio)
        
        if request_data.get("stream", False):
            # Streaming response
            def generate():
                stream_id = f"chatcmpl-{uuid.uuid4().hex}"
                created_time = int(time.time())
                model = request_data.get("model")
                
                # Send initial role delta
                initial_response = {
                    "id": stream_id,
                    "object": "chat.completion.chunk",
                    "created": created_time,
                    "model": model,
                    "choices": [{"delta": {"role": "assistant"}, "index": 0}]
                }
                yield f"data: {json.dumps(initial_response)}\n\n"
                
                try:
                    # Setup WebSocket connection
                    e = {"token": config["token"], "id": draw_id}
                    xtx = KontextFluxEncryptor(config).get_xtx_hash(e)
                    url = f"wss://api.kontextflux.com/client/styleAI/checkWs?xtx={xtx}"

                    ws = websocket.create_connection(url, timeout=30)
                    ws.settimeout(10)  # 设置接收超时
                    ws.send(json.dumps(e))
                    
                    while True:
                        try:
                            msg = ws.recv()
                            data = json.loads(msg)
                            
                            if data.get("content", {}).get("photo"):
                                # Generation completed
                                original_url = data["content"]["photo"]["url"]
                                print(f"🖼️ 原始图片URL: {original_url}")

                                # 下载图片并进行水印去除处理
                                filename = downloadFile(original_url)
                                if filename:
                                    print(f"📥 图片已下载到本地: {filename}")
                                    # 尝试去除水印
                                    watermark_removed_url = watermarkremover(filename)
                                    if watermark_removed_url:
                                        final_url = watermark_removed_url
                                        print(f"✅ 水印去除成功: {final_url}")
                                    else:
                                        final_url = original_url
                                        print(f"⚠️ 水印去除失败，使用原图: {final_url}")
                                else:
                                    final_url = original_url
                                    print(f"⚠️ 图片下载失败，使用原图: {final_url}")

                                # Send final content
                                content_response = {
                                    "id": stream_id,
                                    "created": created_time,
                                    "model": model,
                                    "choices": [{"delta": {"content": f"![image]({final_url})"}, "index": 0}]
                                }
                                yield f"data: {json.dumps(content_response)}\n\n"
                                
                                # Send completion signal
                                complete_response = {
                                    "id": stream_id,
                                    "created": created_time,
                                    "model": model,
                                    "choices": [{"delta": {}, "index": 0, "finish_reason": "stop"}]
                                }
                                yield f"data: {json.dumps(complete_response)}\n\n"
                                yield "data: [DONE]\n\n"
                                break
                            else:
                                # Send progress update
                                progress = data.get("content", {}).get("progress", 0)
                                
                                # 选择适合当前进度的表情符号
                                if progress < 20:
                                    emoji = "🚀"  # 开始阶段
                                elif progress < 40:
                                    emoji = "⚙️"  # 处理中
                                elif progress < 60:
                                    emoji = "✨"  # 半程
                                elif progress < 80:
                                    emoji = "🔍"  # 细节生成
                                elif progress < 100:
                                    emoji = "🎨"  # 最终润色
                                else:
                                    emoji = "✅"  # 完成
                                
                                # 创建进度条
                                bar_length = 20
                                filled_length = int(bar_length * progress / 100)
                                bar = "█" * filled_length + "░" * (bar_length - filled_length)
                                
                                # 格式化美观的进度信息
                                reasoning_text = f"{emoji} 图像生成进度 |{bar}| {progress}%\n"
                                
                                progress_response = {
                                    "id": stream_id,
                                    "created": created_time,
                                    "model": model,
                                    "choices": [{"delta": {"reasoning_content": reasoning_text}, "index": 0}]
                                }
                                yield f"data: {json.dumps(progress_response)}\n\n"
                                
                                # 添加一个小延迟，避免过于频繁的更新
                                time.sleep(0.1)
                                
                        except websocket.WebSocketTimeoutException:
                            continue
                        except Exception as e:
                            print(f"WebSocket error: {e}")
                            break
                    
                    ws.close()
                    
                except Exception as e:
                    print(f"Stream processing error: {e}")
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"
                    yield "data: [DONE]\n\n"
            
            return Response(stream_with_context(generate()), 
                           mimetype="text/event-stream",
                           headers={
                               "Cache-Control": "no-cache",
                               "Connection": "keep-alive",
                               "X-Accel-Buffering": "no"
                           })
        else:
            # Non-streaming response (wait for completion)
            original_url = wait_for_completion(config, draw_id)
            print(f"🖼️ 原始图片URL: {original_url}")

            # 下载图片并进行水印去除处理
            filename = downloadFile(original_url)
            if filename:
                print(f"📥 图片已下载到本地: {filename}")
                # 尝试去除水印
                watermark_removed_url = watermarkremover(filename)
                if watermark_removed_url:
                    final_url = watermark_removed_url
                    print(f"✅ 水印去除成功: {final_url}")
                else:
                    final_url = original_url
                    print(f"⚠️ 水印去除失败，使用原图: {final_url}")
            else:
                final_url = original_url
                print(f"⚠️ 图片下载失败，使用原图: {final_url}")

            return jsonify({
                "id": f"chatcmpl-{uuid.uuid4().hex}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": request_data.get("model"),
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": f"![image]({final_url})"
                    },
                    "index": 0,
                    "finish_reason": "stop"
                }],
                "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
            })
    
    except Exception as e:
        print(f"Error processing request: {e}")
        return {"error": f"Internal server error: {str(e)}"}, 500


def downloadFile(url, filename=None):
    """
    下载图片文件
    返回文件名，如果失败返回None
    """
    filename = filename or str(uuid.uuid4()) + ".png"
    try:
        print(f"📥 开始下载图片: {url}")

        response = requests.get(url, timeout=30)
        response.raise_for_status()

        # 检查内容类型
        content_type = response.headers.get('content-type', '')
        if not content_type.startswith('image/'):
            print(f"⚠️ 警告: 内容类型不是图片: {content_type}")

        # 检查文件大小
        content_length = len(response.content)
        if content_length == 0:
            print(f"❌ 下载的文件为空")
            return None

        print(f"📊 文件大小: {content_length} bytes")

        with open(filename, "wb") as f:
            f.write(response.content)

        print(f"✅ 下载完成: {filename}")
        return filename

    except requests.exceptions.Timeout:
        print(f"⏰ 下载超时: {url}")
        return None
    except requests.exceptions.HTTPError as e:
        print(f"❌ HTTP错误 {e.response.status_code}: {url}")
        return None
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None


def get_x_ebg_param():
    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
    p = timestamp
    x_ebg_param = base64.b64encode(p.encode("utf-8")).decode("utf-8")
    return timestamp, x_ebg_param


def watermarkremover(filename):
    """
    去除图片水印
    返回去除水印后的图片URL，如果失败返回None
    """
    try:
        print(f"🔄 开始水印去除处理: {filename}")

        if not os.path.exists(filename):
            print(f"❌ 文件不存在: {filename}")
            return None

        pixb_cl_id = str(random.randint(1000000000, 9999999999))
        timestamp, x_ebg_param = get_x_ebg_param()

        n = "A4nzUYcDOZ"
        t = f"POST/service/public/transformation/v1.0/predictions/wm/remove{timestamp}{pixb_cl_id}"
        x_ebg_signature = hmac.new(
            n.encode("utf-8"), t.encode("utf-8"), hashlib.sha256
        ).hexdigest()

        url = "https://api.watermarkremover.io/service/public/transformation/v1.0/predictions/wm/remove"
        payload = {"input.rem_text": "false", "input.rem_logo": "false", "retention": "1d"}

        print(f"📤 上传图片到水印去除服务...")

        with open(filename, "rb") as file_handle:
            files = [("input.image", (filename, file_handle.read(), "image/png"))]

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "x-ebg-signature": x_ebg_signature,
                "pixb-cl-id": pixb_cl_id,
                "x-ebg-param": x_ebg_param,
                "origin": "https://www.watermarkremover.io",
                "referer": "https://www.watermarkremover.io/",
            }

            response = requests.post(url, data=payload, files=files, headers=headers, timeout=30)

        if response.status_code != 200:
            print(f"❌ 上传失败，状态码: {response.status_code}")
            return None

        response_data = response.json()
        result_id = response_data.get("_id")

        if not result_id:
            print(f"❌ 未获取到任务ID: {response_data}")
            return None

        print(f"✅ 上传成功，任务ID: {result_id}")
        print(f"⏳ 等待处理完成...")

        # 检查处理状态
        status_url = f"https://api.watermarkremover.io/service/public/transformation/v1.0/predictions/{result_id}"
        status_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "origin": "https://www.watermarkremover.io",
            "referer": "https://www.watermarkremover.io/",
        }

        # 最多等待60秒
        for attempt in range(60):
            try:
                status_response = requests.get(status_url, headers=status_headers, timeout=10)

                if status_response.status_code != 200:
                    print(f"⚠️ 状态查询失败，状态码: {status_response.status_code}")
                    time.sleep(1)
                    continue

                status_data = status_response.json()
                status = status_data.get("status", "UNKNOWN")

                print(f"📊 处理状态: {status} (尝试 {attempt + 1}/60)")

                if status == "SUCCESS":
                    output_urls = status_data.get("output", [])
                    if output_urls and len(output_urls) > 0:
                        result_url = output_urls[0]
                        print(f"🎉 水印去除成功: {result_url}")

                        # 清理临时文件
                        try:
                            if os.path.exists(filename):
                                os.remove(filename)
                                print(f"🗑️ 临时文件已删除: {filename}")
                        except OSError as e:
                            print(f"⚠️ 删除临时文件失败: {e}")

                        return result_url
                    else:
                        print(f"❌ 处理成功但未获取到结果URL: {status_data}")
                        return None

                elif status == "FAILED":
                    print(f"❌ 水印去除失败: {status_data}")
                    return None

                elif status in ["PENDING", "PROCESSING"]:
                    # 继续等待
                    time.sleep(1)
                    continue
                else:
                    print(f"⚠️ 未知状态: {status}")
                    time.sleep(1)
                    continue

            except Exception as e:
                print(f"⚠️ 状态查询出错 (尝试 {attempt + 1}): {e}")
                time.sleep(1)
                continue

        print(f"⏰ 水印去除超时 (60秒)")
        return None

    except Exception as e:
        print(f"❌ 水印去除过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 确保清理临时文件
        try:
            if os.path.exists(filename):
                os.remove(filename)
                print(f"🗑️ 最终清理临时文件: {filename}")
        except:
            pass


def wait_for_completion(config, draw_id: str, timeout: int = 300) -> str:
    """Wait for image generation completion (non-streaming)"""
    e = {"token": config["token"], "id": draw_id}
    xtx = KontextFluxEncryptor(config).get_xtx_hash(e)
    url = f"wss://api.kontextflux.com/client/styleAI/checkWs?xtx={xtx}"

    ws = None
    start_time = time.time()

    try:
        ws = websocket.create_connection(url, timeout=30)
        ws.send(json.dumps(e))
        ws.settimeout(10)  # 设置接收超时

        while True:
            # 检查总超时
            if time.time() - start_time > timeout:
                raise TimeoutError(f"Image generation timeout after {timeout} seconds")

            try:
                msg = ws.recv()
                data = json.loads(msg)

                if data.get("content", {}).get("photo"):
                    return data["content"]["photo"]["url"]

                # 添加小延迟避免过于频繁的请求
                time.sleep(2)

            except websocket.WebSocketTimeoutException:
                # 接收超时，继续尝试
                continue

    except Exception as e:
        print(f"WebSocket error in wait_for_completion: {e}")
        raise
    finally:
        if ws:
            try:
                ws.close()
            except:
                pass


if __name__ == "__main__":
    import sys

    # 强制输出到控制台
    try:
        sys.stdout.reconfigure(line_buffering=True)
    except:
        pass

    print("\n" + "="*50)
    print("🚀 KontextFlux AI Image Generator")
    print("="*50)
    print("🌐 Web界面:")
    print("   http://127.0.0.1:8002/     - 图像生成界面")
    print("   http://127.0.0.1:8002/test - API测试工具")
    print("📡 API端点:")
    print("   GET  /api              - API信息")
    print("   GET  /health           - 健康检查")
    print("   POST /api/generate     - Web生成API")
    print("   GET  /v1/models        - 获取模型列表")
    print("   POST /v1/chat/completions - OpenAI兼容API")
    print(f"🔑 API Keys: {len(VALID_CLIENT_KEYS)} configured")
    print("="*50)
    sys.stdout.flush()

    try:
        print("🔧 正在启动服务器...", flush=True)
        print("📍 服务地址: http://127.0.0.1:8002", flush=True)
        print("🛑 按 Ctrl+C 停止服务器", flush=True)
        print("-" * 40, flush=True)

        # 启动Flask服务器
        app.run(
            host="127.0.0.1",
            port=8002,
            debug=False,
            use_reloader=False,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\n\n✅ 服务器已被用户停止")

    except OSError as e:
        if "10013" in str(e) or "Permission denied" in str(e):
            print(f"\n❌ 端口被占用或权限不足")
            print("💡 解决方案:")
            print("   1. 关闭占用8002端口的程序")
            print("   2. 或以管理员身份运行")
        else:
            print(f"\n❌ 网络错误: {e}")

    except ImportError as e:
        print(f"\n❌ 缺少依赖包: {e}")
        print("💡 请运行: pip install -r requirements.txt")

    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        print("\n🔍 详细错误信息:")
        import traceback
        traceback.print_exc()

    finally:
        print("\n" + "=" * 40)
        print("🔚 程序结束")

    # 防止窗口闪退
    try:
        input("\n按回车键退出...")
    except:
        pass